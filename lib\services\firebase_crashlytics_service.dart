import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

import '../services/environment_service.dart';

/// خدمة Firebase Crashlytics لتتبع الأخطاء والـ crashes في التطبيق
/// تتيح مراقبة استقرار التطبيق وتجميع تقارير مفصلة عن الأخطاء
class FirebaseCrashlyticsService {
  static FirebaseCrashlytics? _crashlytics;
  static bool _isInitialized = false;

  /// الحصول على مثيل Firebase Crashlytics
  static FirebaseCrashlytics? get crashlytics => _crashlytics;

  /// تهيئة Firebase Crashlytics
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // التحقق من تفعيل Firebase Crashlytics في الإعدادات
      if (!EnvironmentService.isFirebaseEnabled() ||
          !EnvironmentService.isFirebaseCrashlyticsEnabled()) {
        return;
      }

      // تهيئة Firebase Core إذا لم يكن مهيأ
      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp();
      }

      // تهيئة Crashlytics
      _crashlytics = FirebaseCrashlytics.instance;

      // تعيين إعدادات جمع البيانات
      await _crashlytics!.setCrashlyticsCollectionEnabled(
        EnvironmentService.isFirebaseCrashlyticsEnabled(),
      );

      // تعيين معالج الأخطاء التلقائي للـ Flutter errors
      FlutterError.onError = (FlutterErrorDetails details) {
        _crashlytics?.recordFlutterFatalError(details);
      };

      // تعيين معالج الأخطاء للـ Dart errors
      PlatformDispatcher.instance.onError = (error, stack) {
        _crashlytics?.recordError(error, stack, fatal: true);
        return true;
      };

      _isInitialized = true;
    } catch (e) {}
  }

  /// تسجيل خطأ مخصص
  static Future<void> recordError(
    dynamic exception,
    StackTrace? stackTrace, {
    String? reason,
    bool fatal = false,
    Map<String, dynamic>? customKeys,
  }) async {
    if (_crashlytics == null || !isEnabled) return;

    try {
      // إضافة المفاتيح المخصصة
      if (customKeys != null) {
        for (final entry in customKeys.entries) {
          await _crashlytics!.setCustomKey(entry.key, entry.value);
        }
      }

      // تسجيل الخطأ
      await _crashlytics!.recordError(
        exception,
        stackTrace,
        reason: reason,
        fatal: fatal,
      );
    } catch (e) {}
  }

  /// تسجيل رسالة مخصصة
  static Future<void> log(String message) async {
    if (_crashlytics == null || !isEnabled) return;

    try {
      await _crashlytics!.log(message);
    } catch (e) {}
  }

  /// تعيين معرف المستخدم
  static Future<void> setUserId(String userId) async {
    if (_crashlytics == null || !isEnabled) return;

    try {
      await _crashlytics!.setUserIdentifier(userId);
    } catch (e) {}
  }

  /// تعيين مفتاح مخصص
  static Future<void> setCustomKey(String key, dynamic value) async {
    if (_crashlytics == null || !isEnabled) return;

    try {
      await _crashlytics!.setCustomKey(key, value);
    } catch (e) {}
  }

  /// تعيين عدة مفاتيح مخصصة
  static Future<void> setCustomKeys(Map<String, dynamic> keys) async {
    if (_crashlytics == null || !isEnabled) return;

    try {
      for (final entry in keys.entries) {
        await _crashlytics!.setCustomKey(entry.key, entry.value);
      }
    } catch (e) {}
  }

  /// إرسال crash تجريبي (للاختبار فقط)
  static Future<void> sendTestCrash() async {
    if (_crashlytics == null || !isEnabled) return;
  }

  /// تسجيل خطأ شبكة
  static Future<void> recordNetworkError({
    required String url,
    required String method,
    required int statusCode,
    required String errorMessage,
    Map<String, dynamic>? additionalData,
  }) async {
    final customKeys = <String, dynamic>{
      'error_type': 'network_error',
      'url': url,
      'method': method,
      'status_code': statusCode,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };

    await recordError(
      Exception('Network Error: $errorMessage'),
      StackTrace.current,
      reason: 'Network request failed: $method $url',
      fatal: false,
      customKeys: customKeys,
    );
  }

  /// تسجيل خطأ قاعدة البيانات
  static Future<void> recordDatabaseError({
    required String operation,
    required String tableName,
    required String errorMessage,
    Map<String, dynamic>? additionalData,
  }) async {
    final customKeys = <String, dynamic>{
      'error_type': 'database_error',
      'operation': operation,
      'table_name': tableName,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };

    await recordError(
      Exception('Database Error: $errorMessage'),
      StackTrace.current,
      reason: 'Database operation failed: $operation on $tableName',
      fatal: false,
      customKeys: customKeys,
    );
  }

  /// تسجيل خطأ مصادقة
  static Future<void> recordAuthError({
    required String authMethod,
    required String errorMessage,
    String? errorCode,
    Map<String, dynamic>? additionalData,
  }) async {
    final customKeys = <String, dynamic>{
      'error_type': 'auth_error',
      'auth_method': authMethod,
      'error_code': errorCode ?? 'unknown',
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };

    await recordError(
      Exception('Authentication Error: $errorMessage'),
      StackTrace.current,
      reason: 'Authentication failed: $authMethod',
      fatal: false,
      customKeys: customKeys,
    );
  }

  /// تسجيل خطأ واجهة المستخدم
  static Future<void> recordUIError({
    required String screenName,
    required String errorMessage,
    String? widgetName,
    Map<String, dynamic>? additionalData,
  }) async {
    final customKeys = <String, dynamic>{
      'error_type': 'ui_error',
      'screen_name': screenName,
      'widget_name': widgetName ?? 'unknown',
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };

    await recordError(
      Exception('UI Error: $errorMessage'),
      StackTrace.current,
      reason: 'UI error in $screenName',
      fatal: false,
      customKeys: customKeys,
    );
  }

  /// إعادة تعيين بيانات Crashlytics
  static Future<void> resetCrashlyticsData() async {
    if (_crashlytics == null || !isEnabled) return;

    try {
      // مسح معرف المستخدم
      await _crashlytics!.setUserIdentifier('');
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في إعادة تعيين بيانات Crashlytics: $e');
      }
    }
  }

  /// التحقق من حالة التهيئة
  static bool get isInitialized => _isInitialized && _crashlytics != null;

  /// التحقق من تفعيل Crashlytics
  static bool get isEnabled =>
      EnvironmentService.isFirebaseEnabled() &&
      EnvironmentService.isFirebaseCrashlyticsEnabled();

  /// الحصول على معلومات حالة الخدمة
  static Map<String, dynamic> getServiceInfo() {
    return {
      'initialized': _isInitialized,
      'enabled': isEnabled,
      'crashlytics_available': _crashlytics != null,
      'firebase_enabled': EnvironmentService.isFirebaseEnabled(),
      'crashlytics_enabled': EnvironmentService.isFirebaseCrashlyticsEnabled(),
    };
  }
}
