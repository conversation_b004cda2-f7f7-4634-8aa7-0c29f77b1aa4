import 'package:flutter/material.dart';
import '../services/odoo_service.dart';
import '../services/firebase_analytics_service.dart';
import '../services/performance_monitoring_service.dart';
import '../models/leave_type.dart';
import '../models/public_holiday.dart';
import '../config/app_config.dart';
import '../generated/l10n/app_localizations.dart';

/// شاشة طلب إجازة جديدة
class LeaveRequestScreen extends StatefulWidget {
  final OdooService odooService;
  final int uid;
  final String password;
  final LeaveType leaveType;

  const LeaveRequestScreen({
    super.key,
    required this.odooService,
    required this.uid,
    required this.password,
    required this.leaveType,
  });

  @override
  State<LeaveRequestScreen> createState() => _LeaveRequestScreenState();
}

class _LeaveRequestScreenState extends State<LeaveRequestScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  DateTime? _dateFrom;
  DateTime? _dateTo;
  double _numberOfDays = 0.0;
  bool _isSubmitting = false;
  List<PublicHoliday> _publicHolidays = [];
  double? _availableBalance;
  bool _isLoadingBalance = false;
  bool _isCalculatingDays = false;

  // متحكمات الحركة
  late AnimationController _cardController;
  late AnimationController _buttonController;
  late Animation<double> _cardAnimation;
  late Animation<double> _buttonAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _loadAvailableBalance();

    // تتبع مشاهدة شاشة طلب الإجازة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FirebaseAnalyticsService.logScreenView(
        screenName: 'leave_request_screen',
        screenClass: 'LeaveRequestScreen',
        parameters: {
          'leave_type': widget.leaveType.name,
          'leave_type_id': widget.leaveType.id,
        },
      );
    });
  }

  @override
  void dispose() {
    _cardController.dispose();
    _buttonController.dispose();
    super.dispose();
  }

  /// تهيئة الحركات
  void _initAnimations() {
    _cardController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );

    _cardAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _cardController, curve: Curves.easeOut));
    _buttonAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _buttonController, curve: Curves.easeOut),
    );

    // بدء الحركات فوراً لتقليل التأخير
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _cardController.forward();
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) _buttonController.forward();
        });
      }
    });
  }

  /// تحميل الرصيد المتاح لنوع الإجازة
  Future<void> _loadAvailableBalance() async {
    setState(() {
      _isLoadingBalance = true;
    });

    try {
      final balance = await widget.odooService.getEmployeeLeaveBalanceForType(
        uid: widget.uid,
        password: widget.password,
        leaveTypeId: widget.leaveType.id,
      );

      if (mounted) {
        setState(() {
          _availableBalance = balance;
          _isLoadingBalance = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _availableBalance = null;
          _isLoadingBalance = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: CustomScrollView(
        slivers: [
          // رأس الصفحة المتحرك مع اسم نوع الإجازة
          _buildAnimatedHeader(),
          // بطاقة المعلومات العلوية
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.only(
                left: AppConfig.spacing,
                right: AppConfig.spacing,
                top: AppConfig.spacing,
              ),
              child: _buildTopInfoCard(),
            ),
          ),
          // محتوى الصفحة
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.all(AppConfig.spacing),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    // بطاقات التواريخ في صف أفقي
                    _buildHorizontalDateCards(),
                    SizedBox(height: AppConfig.largeSpacing),
                    // زر تقديم الطلب
                    ScaleTransition(
                      scale: _buttonAnimation,
                      child: _buildSubmitButton(),
                    ),
                    SizedBox(height: AppConfig.spacing),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقات التواريخ في صف أفقي
  Widget _buildHorizontalDateCards() {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, 0.3),
        end: Offset.zero,
      ).animate(_cardAnimation),
      child: FadeTransition(
        opacity: _cardAnimation,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              // بطاقة تاريخ البداية
              Expanded(
                child: _buildDateCard(
                  title: AppLocalizations.of(context).startdate,
                  date: _dateFrom,
                  icon: Icons.calendar_month,
                  onTap: () => _selectDate(context, true),
                ),
              ),
              const SizedBox(width: 12),
              // أيقونة السهم بين البطاقتين
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF667EEA).withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.arrow_forward,
                  color: Color(0xFF667EEA),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              // بطاقة تاريخ النهاية
              Expanded(
                child: _buildDateCard(
                  title: AppLocalizations.of(context).enddate,
                  date: _dateTo,
                  icon: Icons.event_available,
                  onTap: () => _selectDate(context, false),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء رأس الصفحة مع اسم نوع الإجازة
  Widget _buildAnimatedHeader() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: const Color.fromARGB(255, 44, 44, 209),
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color.fromARGB(255, 139, 193, 255),
                Color.fromARGB(255, 75, 155, 253),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: AppConfig.spacing,
                vertical: AppConfig.smallSpacing,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(height: AppConfig.smallSpacing),
                  Text(
                    AppLocalizations.of(context).newleaverequest,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.leaveType.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة المعلومات العلوية الجديدة
  Widget _buildTopInfoCard() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
      ),
      child: Container(
        padding: EdgeInsets.all(AppConfig.spacing),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
          border: Border.all(color: const Color(0xFFE3F2FD), width: 2),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.shade100,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            // بطاقة الأيام المحددة
            Expanded(
              child: _buildSmallInfoBox(
                icon: Icons.calendar_today,
                title: AppLocalizations.of(context).numberOfDays,
                value: _isCalculatingDays
                    ? '...'
                    : _numberOfDays > 0
                    ? '${_numberOfDays.toStringAsFixed(1)} ${AppLocalizations.of(context).day}'
                    : '--',
                color: const Color(0xFF2196F3),
                iconColor: Colors.white,
              ),
            ),
            // فاصل
            Container(width: 1, height: 40, color: Colors.grey.shade200),
            // بطاقة الرصيد المتاح
            Expanded(
              child: _buildSmallInfoBox(
                icon: Icons.account_balance_wallet,
                title: AppLocalizations.of(context).availablebalance,
                value: _isLoadingBalance
                    ? '...'
                    : _availableBalance != null
                    ? '${_availableBalance!.toStringAsFixed(1)} ${AppLocalizations.of(context).day}'
                    : '--',
                color: const Color(0xFF4CAF50),
                iconColor: Colors.white,
              ),
            ),
            if (_publicHolidays.isNotEmpty) ...[
              // فاصل
              Container(width: 1, height: 40, color: Colors.grey.shade200),
              // بطاقة الإجازات العامة
              Expanded(
                child: _buildSmallInfoBox(
                  icon: Icons.celebration,
                  title: AppLocalizations.of(context).publicholidays,
                  value: '${_publicHolidays.length}',
                  color: const Color(0xFFFF9800),
                  iconColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة التاريخ المعدلة للعرض الأفقي
  Widget _buildDateCard({
    required String title,
    required DateTime? date,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: const Color(0xFFE3F2FD), width: 1.5),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF90CAF9).withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة التاريخ
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF667EEA).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: const Color(0xFF667EEA), size: 20),
            ),
            const SizedBox(height: 8),
            // العنوان
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 6),
            // التاريخ (بدون أرقام عربية)
            Text(
              date != null
                  ? _formatDate(date) // استخدام الدالة الجديدة بدون أرقام عربية
                  : AppLocalizations.of(context).selectdate,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: date != null
                    ? const Color(0xFF0D47A1)
                    : Colors.grey.shade400,
              ),
              textAlign: TextAlign.center,
            ),
            if (date != null) ...[
              const SizedBox(height: 4),
              Text(
                _getArabicDayName(date.weekday),
                style: TextStyle(fontSize: 10, color: Colors.grey.shade500),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء مربع معلومات صغير
  Widget _buildSmallInfoBox({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
    required Color iconColor,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppConfig.spacing,
        vertical: AppConfig.smallSpacing,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // الأيقونة
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
            child: Icon(icon, size: 16, color: iconColor),
          ),
          const SizedBox(height: 4),
          // القيمة (بدون أرقام عربية)
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          // العنوان
          Text(
            title,
            style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// بناء زر تقديم الطلب
  Widget _buildSubmitButton() {
    return Container(
      width: double.infinity,
      height: 60,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: _canSubmit()
              ? [
                  const Color.fromARGB(255, 89, 190, 248),
                  const Color.fromARGB(255, 111, 183, 255),
                ]
              : [
                  const Color.fromARGB(255, 249, 248, 248),
                  const Color.fromARGB(255, 129, 180, 245),
                ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        boxShadow: _canSubmit()
            ? [
                BoxShadow(
                  color: const Color(0xFF667EEA).withValues(alpha: 0.4),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ]
            : [],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _canSubmit() ? _submitRequest : null,
          borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
          child: Center(
            child: _isSubmitting
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.send, color: Colors.white, size: 24),
                      SizedBox(width: AppConfig.spacing),
                      Text(
                        AppLocalizations.of(context).submitleaverequest,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  /// تنسيق التاريخ بدون أرقام عربية
  String _formatDate(DateTime date) {
    final day = date.day.toString().padLeft(2, '0');
    final month = date.month.toString().padLeft(2, '0');
    final year = date.year.toString();
    return '$year/$month/$day'; // إرجاع الأرقام الإنجليزية مباشرة
  }

  /// الحصول على اسم اليوم بالعربية
  String _getArabicDayName(int weekday) {
    switch (weekday) {
      case 1:
        return AppLocalizations.of(context).daysoftheweek1;
      case 2:
        return AppLocalizations.of(context).daysoftheweek2;
      case 3:
        return AppLocalizations.of(context).daysoftheweek3;
      case 4:
        return AppLocalizations.of(context).daysoftheweek4;
      case 5:
        return AppLocalizations.of(context).daysoftheweek5;
      case 6:
        return AppLocalizations.of(context).daysoftheweek6;
      case 7:
        return AppLocalizations.of(context).daysoftheweek7;
      default:
        return '';
    }
  }

  /// اختيار التاريخ
  Future<void> _selectDate(BuildContext context, bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          _dateFrom = picked;
          // إذا كان تاريخ النهاية أقل من تاريخ البداية، قم بإعادة تعيينه
          if (_dateTo != null && _dateTo!.isBefore(_dateFrom!)) {
            _dateTo = null;
            _numberOfDays = 0.0;
          }
        } else {
          _dateTo = picked;
        }
      });

      // حساب الأيام بعد تحديث التواريخ
      _calculateDays();
    }
  }

  /// حساب عدد الأيام (أيام العمل الفعلية) مع جلب الإجازات العامة
  Future<void> _calculateDays() async {
    if (_dateFrom == null || _dateTo == null) {
      return;
    }

    setState(() {
      _isCalculatingDays = true;
      _publicHolidays = [];
    });

    try {
      // جلب الإجازات العامة للفترة المحددة
      final publicHolidays = await widget.odooService.getPublicHolidaysDetailed(
        uid: widget.uid,
        password: widget.password,
        dateFrom: _dateFrom!,
        dateTo: _dateTo!,
      );

      // حساب أيام العمل الفعلية باستخدام خدمة Odoo
      final workingDays = await widget.odooService.calculateWorkingDays(
        uid: widget.uid,
        password: widget.password,
        dateFrom: _dateFrom!,
        dateTo: _dateTo!,
      );

      if (mounted) {
        setState(() {
          _numberOfDays = workingDays ?? _calculateManualDays();
          _publicHolidays = publicHolidays ?? [];
          _isCalculatingDays = false;
        });
      }
    } catch (e) {
      // في حالة الخطأ، استخدم الحساب اليدوي
      if (mounted) {
        setState(() {
          _numberOfDays = _calculateManualDays();
          _publicHolidays = [];
          _isCalculatingDays = false;
        });
      }
    }
  }

  /// حساب الأيام يدوياً (بدون استدعاء API)
  double _calculateManualDays() {
    if (_dateFrom == null || _dateTo == null) return 0.0;

    int days = 0;
    DateTime currentDate = _dateFrom!;

    while (currentDate.isBefore(_dateTo!) ||
        currentDate.isAtSameMomentAs(_dateTo!)) {
      // استثناء أيام الجمعة والسبت (نهاية الأسبوع)
      if (currentDate.weekday != DateTime.friday &&
          currentDate.weekday != DateTime.saturday) {
        days++;
      }
      currentDate = currentDate.add(const Duration(days: 1));
    }

    return days.toDouble();
  }

  /// التحقق من إمكانية تقديم الطلب
  bool _canSubmit() {
    return _dateFrom != null &&
        _dateTo != null &&
        _numberOfDays > 0 &&
        !_isSubmitting;
  }

  /// تقديم طلب الإجازة
  Future<void> _submitRequest() async {
    if (!_formKey.currentState!.validate() || !_canSubmit()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // تتبع محاولة تقديم طلب إجازة
      await FirebaseAnalyticsService.logEvent(
        name: 'leave_request_attempt',
        parameters: {
          'leave_type': widget.leaveType.name,
          'leave_type_id': widget.leaveType.id,
          'days_requested': _numberOfDays,
        },
      );

      // التحقق من رصيد الإجازات أولاً مع تتبع الأداء
      final availableBalance =
          await PerformanceMonitoringService.traceDatabaseOperation(
            operationType: 'read',
            tableName: 'hr.leave.allocation',
            operation: () => widget.odooService.getEmployeeLeaveBalanceForType(
              uid: widget.uid,
              password: widget.password,
              leaveTypeId: widget.leaveType.id,
            ),
          );

      if (availableBalance == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'لا يمكن التحقق من رصيد الإجازات. يرجى المحاولة لاحقاً',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // التحقق من كفاية الرصيد
      if (availableBalance < _numberOfDays) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'لا يوجد لديك رصيد كافي من الإجازات\n'
                'الرصيد المتاح: ${availableBalance.toStringAsFixed(1)} يوم\n'
                'الأيام المطلوبة: ${_numberOfDays.toStringAsFixed(1)} يوم',
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
        return;
      }

      // الحصول على معرف الموظف
      final employeeId = await widget.odooService.getEmployeeId(
        uid: widget.uid,
        password: widget.password,
      );

      if (employeeId == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لم يتم العثور على بيانات الموظف'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // إعداد البيانات المطلوبة لـ Odoo
      final dateFromStr = _dateFrom!.toIso8601String().split('T')[0];
      final dateToStr = _dateTo!.toIso8601String().split('T')[0];

      final leaveRequestData = {
        'holiday_status_id': widget.leaveType.id,
        // التواريخ للعرض الداخلي
        'request_date_from': dateFromStr,
        'request_date_to': dateToStr,
        // التواريخ للعرض الخارجي (مع الوقت)
        'date_from': '$dateFromStr 08:00:00',
        'date_to': '$dateToStr 17:00:00',
        'number_of_days': _numberOfDays,
        'state': 'draft',
        'holiday_type': 'employee', // نوع الإجازة للموظف
        'employee_id': employeeId, // معرف الموظف الصحيح
      };

      // تقديم طلب الإجازة مع تتبع الأداء
      final result = await PerformanceMonitoringService.traceDatabaseOperation(
        operationType: 'create',
        tableName: 'hr.leave',
        operation: () => widget.odooService.createLeaveRequest(
          uid: widget.uid,
          password: widget.password,
          leaveRequestData: leaveRequestData,
        ),
      );

      if (result.success && mounted) {
        // تتبع نجاح تقديم طلب الإجازة
        await FirebaseAnalyticsService.logLeaveRequest(
          leaveType: widget.leaveType.name,
          daysRequested: _numberOfDays.round(),
          status: 'submitted',
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم إنشاء وتقديم طلب الإجازة للموافقة بنجاح!\nرقم الطلب: ${result.leaveId}',
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );
          Navigator.pop(context, true);
        }
      } else {
        // تتبع فشل تقديم طلب الإجازة
        await FirebaseAnalyticsService.logEvent(
          name: 'leave_request_failed',
          parameters: {
            'leave_type': widget.leaveType.name,
            'days_requested': _numberOfDays,
            'error_type': 'submission_failed',
            'error_message': result.errorMessage ?? 'unknown_error',
          },
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                result.errorMessage ??
                    'فشل في تقديم طلب الإجازة\n'
                        'تحقق من:\n'
                        '• صحة التواريخ المحددة\n'
                        '• توفر رصيد كافي من الإجازات\n'
                        '• عدم تداخل مع إجازات أخرى',
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 6),
            ),
          );
        }
      }
    } catch (e) {
      // تتبع خطأ تقديم طلب الإجازة
      await FirebaseAnalyticsService.logError(
        errorType: 'leave_request_exception',
        errorMessage: e.toString(),
        screenName: 'leave_request_screen',
        functionName: '_submitRequest',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}
